//
//  UserInformationEditingPage.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/5/15.
//
//  用户信息编辑页

import Foundation
import UIKit
import Kingfisher
import MBProgressHUD

class UserInformationEditingPage: BaseViewController {
    
    // MARK: - UI组件
    
    private lazy var avatarContainerView: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 112))
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "default_avatar")
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 40
        imageView.layer.borderWidth = 3
        imageView.layer.borderColor = UIColor.orange.cgColor
        imageView.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        imageView.addGestureRecognizer(tapGesture)
        return imageView
    }()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorInset = UIEdgeInsets(top: 0, left: 15, bottom: 0, right: 15)
        tableView.register(UserInfoCell.self, forCellReuseIdentifier: "UserInfoCell")
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 80, right: 0) // 为底部按钮腾出空间
        tableView.sectionFooterHeight = 0 // 取消section默认的footer高度
        return tableView
    }()
    
    private lazy var saveButtonContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        // 添加顶部分隔线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#E7E7E7", alpha: 0.6)
        view.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }
        return view
    }()
    
    private lazy var saveButton: GradientButton = {
        let button = GradientButton(type: .system)
        button.setTitle("保存", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        button.startColor = UIColor(hex: "#FF8F44")  // 浅橙色
        button.endColor = UIColor(hex: "#FF5F22")    // 深橙色
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - 数据模型
    
    struct UserInfoSection {
        var items: [UserInfoItem]
    }
    
    struct UserInfoItem {
        let title: String
        var value: String
        let hasDisclosure: Bool
        var isSelected: Bool // 标识是否已选择或编辑
    }
    
    private var userInfoSections: [UserInfoSection] = []
    
    // 记录树小柒号是否已经编辑过
    private var hasEditedTreeNum: Bool = false
    
    private var customerInfoData: CustomerInfoData?
    // 用于保存接口的可变数据源；初始化后在各子页面直接修改其字段
    private var saveRequest = SaveCustomerInfoRequest()
    private var isLoading: Bool = false

    // 保存初始数据，用于检测是否有修改
    private var originalSaveRequest = SaveCustomerInfoRequest()
    
    // MARK: - 简介相关属性
    private var personalityMentionedUser: [String: String] = [:]
    private var personalitySnapshot: String = ""
    private var personalityEndDate: String = ""
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configureBaseSettings()
        setupUI()
        fetchCustomerInfo()
    }
    
    // MARK: - 私有方法
    
    private func configureBaseSettings() {
        // 设置导航栏标题
        navTitle = "编辑资料"
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        // 显示返回按钮
        showBackButton = true
        // 设置背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        // 确保导航栏可见
        navigationController?.setNavigationBarHidden(false, animated: false)
    }
    
    private func setupUI() {
        // 设置头像容器视图
        avatarContainerView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        // 将头像容器视图设置为表格的头部视图
        tableView.tableHeaderView = avatarContainerView
        
        // 添加表格到内容视图
        contentView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加保存按钮容器
        contentView.addSubview(saveButtonContainer)
        saveButtonContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(80 + WindowUtil.safeAreaBottom) // 考虑底部安全区域
        }
        
        // 添加保存按钮到容器中
        saveButtonContainer.addSubview(saveButton)
        saveButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(44)
        }
    }
    
    private func fetchCustomerInfo() {
        isLoading = true
        // 可选：显示加载指示器
        APIManager.shared.getCustomerInfo { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false
                // 可选：隐藏加载指示器
                switch result {
                case .success(let response):
                    self?.customerInfoData = response.data
                    // 将拉取的数据映射为保存请求体，后续编辑随时修改此对象
                    if let data = response.data {
                        self?.saveRequest = SaveCustomerInfoRequest(from: data)
                        // 保存初始数据的副本，用于检测修改
                        self?.originalSaveRequest = SaveCustomerInfoRequest(from: data)
                    }
                    self?.fillUserInfoSections(with: response.data!)
                case .failure(let error):
                    self?.showToast(error.localizedDescription)
                }
            }
        }
    }
    
    private func fillUserInfoSections(with data: CustomerInfoData) {
        userInfoSections = [
            UserInfoSection(items: [
                UserInfoItem(title: "名字", value: data.nickName?.name ?? "", hasDisclosure: true, isSelected: !(data.nickName?.name.isEmpty ?? true)),
                UserInfoItem(title: "树小柒号", value: data.customerAccount?.account ?? "", hasDisclosure: true, isSelected: !(data.customerAccount?.account.isEmpty ?? true)),
                UserInfoItem(title: "背景图", value: data.backgroundImage ?? "", hasDisclosure: true, isSelected: (data.backgroundImage != nil && !(data.backgroundImage ?? "").isEmpty))
            ]),
            UserInfoSection(items: [
                UserInfoItem(title: "简介", value: data.personalitySign?.snapshot ?? "", hasDisclosure: true, isSelected: !(data.personalitySign?.snapshot ?? "").isEmpty)
            ]),
            UserInfoSection(items: [
                UserInfoItem(title: "性别", value: (data.sexInfo?.sex == 1 ? "男" : data.sexInfo?.sex == 2 ? "女" : "请选择"), hasDisclosure: true, isSelected: (data.sexInfo?.sex != nil)),
                UserInfoItem(title: "生日", value: data.birthdayInfo?.birthday ?? "选择生日", hasDisclosure: true, isSelected: (data.birthdayInfo?.birthday != nil)),
                UserInfoItem(title: "地区", value: (data.region?.regionNames?.joined(separator: " ") ?? ""), hasDisclosure: true, isSelected: !(data.region?.regionNames?.isEmpty ?? true)),
                UserInfoItem(title: "职业", value: (data.job?.jobs?.joined(separator: ", ") ?? ""), hasDisclosure: true, isSelected: !(data.job?.jobs?.isEmpty ?? true)),
                UserInfoItem(title: "学校", value: data.schoolInfo?.school ?? "", hasDisclosure: true, isSelected: !(data.schoolInfo?.school ?? "").isEmpty)
            ])
        ]
        // 头像加载
        if let avatarURL = URL(string: data.wxAvator), !data.wxAvator.isEmpty {
            avatarImageView.kf.setImage(with: avatarURL, placeholder: UIImage(named: "default_avatar"))
        }
        tableView.reloadData()
        
        if let sign = data.personalitySign {
            personalitySnapshot = sign.snapshot ?? ""
            if let dict = sign.mentionedUser {
                personalityMentionedUser = dict
            } else {
                personalityMentionedUser = [:]
            }
            personalityEndDate = sign.endDate ?? ""
        }
        
        // 保存是否可编辑信息
        hasEditedTreeNum = !(data.customerAccount?.isAllowModify ?? true)
    }
    
    // MARK: - 验证方法

    /// 检测用户信息是否有修改
    /// - Returns: 如果有修改返回true，否则返回false
    private func hasDataChanged() -> Bool {
        // 比较各个字段是否有变化
        if saveRequest.backgroundImage != originalSaveRequest.backgroundImage {
            return true
        }

        if saveRequest.customerAccount != originalSaveRequest.customerAccount {
            return true
        }

        if saveRequest.nickName != originalSaveRequest.nickName {
            return true
        }

        if saveRequest.personalitySign != originalSaveRequest.personalitySign {
            return true
        }

        if saveRequest.wxAvator != originalSaveRequest.wxAvator {
            return true
        }

        // 比较生日信息
        if let originalBirthday = originalSaveRequest.birthdayInfo?.birthday,
           let currentBirthday = saveRequest.birthdayInfo?.birthday {
            if originalBirthday != currentBirthday {
                return true
            }
        } else if originalSaveRequest.birthdayInfo?.birthday != saveRequest.birthdayInfo?.birthday {
            return true
        }

        if originalSaveRequest.birthdayInfo?.isShowAge != saveRequest.birthdayInfo?.isShowAge {
            return true
        }

        if originalSaveRequest.birthdayInfo?.isShowConstellation != saveRequest.birthdayInfo?.isShowConstellation {
            return true
        }

        // 比较性别信息
        if originalSaveRequest.sexInfo?.sex != saveRequest.sexInfo?.sex {
            return true
        }

        if originalSaveRequest.sexInfo?.isShow != saveRequest.sexInfo?.isShow {
            return true
        }

        // 比较地区信息
        if originalSaveRequest.region?.regionNames != saveRequest.region?.regionNames {
            return true
        }

        if originalSaveRequest.region?.isShow != saveRequest.region?.isShow {
            return true
        }

        // 比较职业信息
        if originalSaveRequest.job?.jobs != saveRequest.job?.jobs {
            return true
        }

        if originalSaveRequest.job?.isShow != saveRequest.job?.isShow {
            return true
        }

        // 比较学校信息
        if originalSaveRequest.schoolInfo?.school != saveRequest.schoolInfo?.school {
            return true
        }

        if originalSaveRequest.schoolInfo?.admissionTime != saveRequest.schoolInfo?.admissionTime {
            return true
        }

        if originalSaveRequest.schoolInfo?.isShow != saveRequest.schoolInfo?.isShow {
            return true
        }

        return false
    }

    /// 在保存前验证名字是否包含无效字符
    /// - Returns: 如果名字无效，返回错误信息；如果有效，返回nil
    private func validateNameBeforeSave() -> String? {
        let name = saveRequest.nickName.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果名字为空，不需要验证
        if name.isEmpty {
            return nil
        }

        // 检查是否包含无效字符（全部英文符号，包括下划线）
        let invalidCharacters = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "-", "_", "=", "+", "[", "]", "{", "}", "\\", "|", ";", ":", "'", "\"", ",", ".", "<", ">", "/", "?", "`", "~"]
        for char in invalidCharacters {
            if name.contains(char) {
                return "名字不能包含无效字符：\(char)"
            }
        }

        // 检查长度（2-24个字符）
        if name.count < 2 {
            return "名字至少需要2个字符"
        }
        if name.count > 24 {
            return "名字不能超过24个字符"
        }

        return nil
    }

    // MARK: - 事件处理

    /// 重写返回按钮点击事件，检测是否有修改
    override func backButtonTapped() {
        // 检测是否有数据修改
        if hasDataChanged() {
            // 有修改，显示确认弹窗
            showExitConfirmAlert()
        } else {
            // 没有修改，直接返回
            super.backButtonTapped()
        }
    }

    /// 显示退出确认弹窗
    private func showExitConfirmAlert() {
        let alert = CommonAlertView(
            title: "信息暂未保存，是否直接退出？",
            message: "",
            leftButtonTitle: "退出",
            rightButtonTitle: "继续编辑"
        )

        alert.onLeftButtonTap = { [weak self] in
            alert.dismiss()
            // 用户选择退出，直接返回
            self?.performExit()
        }

        alert.onRightButtonTap = {
            alert.dismiss()
            // 用户选择继续编辑，什么都不做
        }

        alert.show()
    }

    /// 执行退出操作
    private func performExit() {
        // 尝试返回上一页
        if let navigationController = navigationController, navigationController.viewControllers.count > 1 {
            navigationController.popViewController(animated: true)
        } else {
            // 尝试关闭当前控制器
            dismiss(animated: true, completion: nil)
        }
    }

    @objc private func avatarTapped() {
        // 跳转到头像预览控制器，支持UIImage和URL
        let previewVC = ImageCropPreviewController(
            image: avatarImageView.image,
            imageURL: nil, // 如有URL可传
            cropRatio: CGSize(width: 1, height: 1),
            confirmButtonTitle: "更换头像",
            descriptionText: ""
        ) { [weak self] newImage, urlStr in
            guard let self = self else { return }
            self.avatarImageView.image = newImage
            self.saveRequest.wxAvator = urlStr
            print("[DEBUG] 头像裁剪完成，URL: \(urlStr)")
        }
        present(previewVC, animated: true)
    }
    
    @objc private func saveButtonTapped() {
        // 在保存前验证名字
        if let nameValidationError = validateNameBeforeSave() {
            showToast(nameValidationError)
            return
        }

        // 显示加载HUD
        MBProgressHUD.showAdded(to: self.view, animated: true)
        print("[DEBUG] 开始保存用户信息...")

        do {
            let jsonData = try JSONEncoder().encode(saveRequest)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("[DEBUG] 保存用户信息请求参数: \(jsonString)")
            }

            // 调用保存资料的 API
            if let params = saveRequest.toDictionary() {
                APIManager.shared.setUserInfo(params: params) { [weak self] result in
                    DispatchQueue.main.async {
                        guard let self = self else { return }
                        MBProgressHUD.hide(for: self.view, animated: true)
                        
                        switch result {
                        case .success:
                            print("[DEBUG] 保存成功，显示HUD提示")
                            let hud = MBProgressHUD.showAdded(to: self.view, animated: true)
                            hud.mode = .text
                            hud.label.text = "保存成功"
                            hud.hide(animated: true, afterDelay: 2.0) // 延长显示时间

                            // 发送通知，通知我的页面刷新数据
                            NotificationCenter.default.post(name: NSNotification.Name("UserInfoUpdatedNotification"), object: nil)

                            // 保存成功后返回上一级页面
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                self.navigationController?.popViewController(animated: true)
                            }
                        case .failure(let error):
                            print("[DEBUG] 保存失败: \(error.localizedDescription)")
                            let hud = MBProgressHUD.showAdded(to: self.view, animated: true)
                            hud.mode = .text
                            hud.label.text = error.localizedDescription
                            hud.hide(animated: true, afterDelay: 2.0)
                        }
                    }
                }
            } else {
                MBProgressHUD.hide(for: self.view, animated: true)
                let hud = MBProgressHUD.showAdded(to: self.view, animated: true)
                hud.mode = .text
                hud.label.text = "参数组装失败"
                hud.hide(animated: true, afterDelay: 2.0)
            }
        } catch {
            MBProgressHUD.hide(for: self.view, animated: true)
            print("[ERROR] 编码保存请求参数失败: \(error.localizedDescription)")
            let hud = MBProgressHUD.showAdded(to: self.view, animated: true)
            hud.mode = .text
            hud.label.text = "保存失败，请稍后重试"
            hud.hide(animated: true, afterDelay: 2.0)
        }
    }
    
    func handleGenderEdit(_ item: UserInfoItem) {
        // 从保存请求体中获取当前性别与展示开关，保证数据一致
        let currentGender: Gender = {
            guard let sex = saveRequest.sexInfo?.sex else { return .none }
            switch sex {
            case 1: return .male
            case 2: return .female
            default: return .none
            }
        }()

        let isShowGender: Bool = saveRequest.sexInfo?.isShow ?? true
        
        let genderEditVC = UserInfoEditGenderViewController()
        genderEditVC.selectedGender = currentGender
        genderEditVC.isShowGender = isShowGender
        genderEditVC.onConfirm = { [weak self] newGender, newShowGender in
            guard let self = self else { return }
            // 更新性别
            if let section = self.findSectionIndex(for: "性别"), let row = self.findRowIndex(in: section, for: "性别") {
                var updatedItem = self.userInfoSections[section].items[row]
                switch newGender {
                case .male:
                    updatedItem.value = "男"
                    self.ensureSexInfoNotNil()
                    self.saveRequest.sexInfo?.sex = 1
                case .female:
                    updatedItem.value = "女"
                    self.ensureSexInfoNotNil()
                    self.saveRequest.sexInfo?.sex = 2
                default:
                    updatedItem.value = "请选择"
                    self.ensureSexInfoNotNil()
                    self.saveRequest.sexInfo?.sex = 0
                }
                updatedItem.isSelected = (newGender != .none)
                self.userInfoSections[section].items[row] = updatedItem
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
                self.ensureSexInfoNotNil()
                self.saveRequest.sexInfo?.isShow = newShowGender
            }
        }
        self.navigationController?.pushViewController(genderEditVC, animated: true)
    }
    
    /// 确保 saveRequest.sexInfo 已实例化，便于后续赋值
    private func ensureSexInfoNotNil() {
        if saveRequest.sexInfo == nil {
            saveRequest.sexInfo = SexInfo()
        }
    }
    
    // MARK: - 简介编辑处理
    private func handleSignatureEdit(_ item: UserInfoItem) {
        // 检查是否还有可用的编辑次数
        if let operableNumber = customerInfoData?.personalitySign?.operableNumber, operableNumber <= 0 {
            // 获取过期时间
            let endDateStr = customerInfoData?.personalitySign?.endDate ?? ""
            if !endDateStr.isEmpty {
                showToast("7天内仅可修改3次简介，\(endDateStr)后可修改。")
            } else {
                showToast("您的签名编辑次数已用完")
            }
            return
        }
        
        let signatureEditVC = UserInfoEditSignatureViewController()
        signatureEditVC.currentSignature = personalitySnapshot
        signatureEditVC.mentionedUserDict = personalityMentionedUser
        
        // 如果有剩余次数，显示在编辑页面
        if let operableNumber = customerInfoData?.personalitySign?.operableNumber {
            signatureEditVC.remainingEdits = operableNumber
        }
        
        // 传递过期时间
        if let endDate = customerInfoData?.personalitySign?.endDate {
            signatureEditVC.endDate = endDate
        }
        
        signatureEditVC.onSignatureUpdated = { [weak self] newSnapshot, newMentionedUser in
            guard let self = self else { return }
            self.personalitySnapshot = newSnapshot
            self.personalityMentionedUser = newMentionedUser
            // 更新数据源和UI
            if let section = self.findSectionIndex(for: "简介"), let row = self.findRowIndex(in: section, for: "简介") {
                var updatedItem = self.userInfoSections[section].items[row]
                updatedItem.value = newSnapshot
                updatedItem.isSelected = !newSnapshot.isEmpty
                self.userInfoSections[section].items[row] = updatedItem
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
                // 更新保存请求
                self.saveRequest.personalitySign = newSnapshot
            }
        }
        self.navigationController?.pushViewController(signatureEditVC, animated: true)
    }
    
    // MARK: - 职业编辑处理
    private func handleOccupationEdit(_ item: UserInfoItem) {
        let occupationEditVC = UserInfoEditOccupationViewController()
        // 假设 item.value 是以特定分隔符（如逗号）连接的字符串，或你需要从其他地方获取职业数组
        let currentOccupations = item.value.split(separator: ",").map(String.init).filter { !$0.isEmpty }
        occupationEditVC.currentOccupations = currentOccupations
        // 将当前展示状态传递给子页面，默认为 true
        occupationEditVC.isShowOccupationPublicly = saveRequest.job?.isShow ?? true
        
        occupationEditVC.onConfirm = { [weak self] updatedOccupations, updatedIsShowPublicly in
            guard let self = self else { return }
            print("[OccupationEditCallback] received occupations: \(updatedOccupations), isShow: \(updatedIsShowPublicly)")
            if let section = self.findSectionIndex(for: "职业"), let row = self.findRowIndex(in: section, for: "职业") {
                var updatedItem = self.userInfoSections[section].items[row]
                updatedItem.value = updatedOccupations.joined(separator: ", ") // 更新为逗号分隔的字符串
                updatedItem.isSelected = !updatedOccupations.isEmpty
                self.userInfoSections[section].items[row] = updatedItem
                if self.saveRequest.job == nil { self.saveRequest.job = Job() }
                self.saveRequest.job?.jobs = updatedOccupations
                self.saveRequest.job?.isShow = updatedIsShowPublicly
                print("[OccupationEditCallback] saveRequest.job after update: jobs=\(self.saveRequest.job?.jobs ?? []), isShow=\(self.saveRequest.job?.isShow ?? false)")
                // TODO: 保存 updatedIsShowPublicly 到你的数据模型
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
            }
        }
        self.navigationController?.pushViewController(occupationEditVC, animated: true)
    }
    
    private func showBirthdayPicker(currentValue: String?, completion: @escaping (String) -> Void) {
        let popup = BirthdayPickerPopup(frame: UIScreen.main.bounds)
        if let currentValue = currentValue, let date = DateFormatter.birthdayFormatter.date(from: currentValue) {
            popup.setDate(date)
        }
        popup.onCancel = { }
        popup.onConfirm = { date in
            let dateStr = DateFormatter.birthdayFormatter.string(from: date)
            completion(dateStr)
        }
        UIApplication.shared.windows.first { $0.isKeyWindow }?.addSubview(popup)
    }
    
    // MARK: - 学校编辑处理
    private func handleSchoolEdit(_ item: UserInfoItem) {
        let schoolEditVC = UserInfoEditSchoolViewController()
        // 从 saveRequest.schoolInfo 读取完整信息
        schoolEditVC.currentSchool = saveRequest.schoolInfo?.school ?? item.value
        schoolEditVC.currentEnrollDate = saveRequest.schoolInfo?.admissionTime ?? ""
        schoolEditVC.isShowSchoolPublicly = saveRequest.schoolInfo?.isShow ?? true
        schoolEditVC.onConfirm = { [weak self] schoolName, enrollYear, isShow in
            guard let self = self else { return }
            if let section = self.findSectionIndex(for: "学校"), let row = self.findRowIndex(in: section, for: "学校") {
                var updatedItem = self.userInfoSections[section].items[row]
                updatedItem.value = schoolName
                updatedItem.isSelected = !schoolName.isEmpty
                self.userInfoSections[section].items[row] = updatedItem
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
                self.ensureSchoolInfoNotNil()
                self.saveRequest.schoolInfo?.school = schoolName
                self.saveRequest.schoolInfo?.admissionTime = enrollYear
                self.saveRequest.schoolInfo?.isShow = isShow
                // TODO: 保存enrollYear和isShow到数据模型
            }
        }
        self.navigationController?.pushViewController(schoolEditVC, animated: true)
    }
    
    /// 确保 saveRequest.schoolInfo 已实例化
    private func ensureSchoolInfoNotNil() {
        if saveRequest.schoolInfo == nil {
            saveRequest.schoolInfo = SchoolInfo()
        }
    }
}

// MARK: - 渐变按钮

class GradientButton: UIButton {
    var startColor: UIColor = .orange {
        didSet {
            updateGradient()
        }
    }
    
    var endColor: UIColor = .red {
        didSet {
            updateGradient()
        }
    }
    
    private var gradientLayer: CAGradientLayer?
    
    override func layoutSubviews() {
        super.layoutSubviews()
        updateGradient()
    }
    
    private func updateGradient() {
        // 移除现有的渐变层
        gradientLayer?.removeFromSuperlayer()
        
        // 创建新的渐变层
        let gradient = CAGradientLayer()
        gradient.colors = [startColor.cgColor, endColor.cgColor]
        gradient.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradient.endPoint = CGPoint(x: 1.0, y: 0.5)
        gradient.frame = bounds
        gradient.cornerRadius = layer.cornerRadius
        
        // 插入到最底层，确保文字在上面
        layer.insertSublayer(gradient, at: 0)
        gradientLayer = gradient
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension UserInformationEditingPage: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return userInfoSections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return userInfoSections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "UserInfoCell", for: indexPath) as! UserInfoCell
        let item = userInfoSections[indexPath.section].items[indexPath.row]
        if item.title == "简介" {
            let attr = UserInformationEditingPage.makeHighlightedSignature(from: personalitySnapshot, mentionedUser: personalityMentionedUser)
            cell.configure(with: item.title, value: item.value, hasDisclosure: item.hasDisclosure, isSelected: item.isSelected, attributedValue: attr)
        } else {
            cell.configure(with: item.title, value: item.value, hasDisclosure: item.hasDisclosure, isSelected: item.isSelected)
        }
        if item.title == "背景图" {
            let imageUrl = item.value.isEmpty ? nil : item.value
            cell.configureWithBackgroundImage(isSelected: item.isSelected, imageUrl: imageUrl)
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let item = userInfoSections[indexPath.section].items[indexPath.row]
        
        // 为背景图行提供更高的高度
        if item.title == "背景图" {
            return 50
        } else if item.title == "简介" {
            return 100
        }
        
        return 45
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = userInfoSections[indexPath.section].items[indexPath.row]
        print("选择了: \(item.title)")
        
        // 这里可以处理选择后的状态更新
        // 假设选择后设置为已选择状态
        // 在实际应用中，你可能需要根据用户的操作来决定是否更新状态
        if item.title == "名字" {
            handleNameEdit(item)
        } else if item.title == "树小柒号" {
            handleNumEdit(item)
        } else if indexPath.section == 1 && indexPath.row == 0 {
            // 简介
            handleSignatureEdit(item)
        } else if item.title == "背景图" {
            let previewVC = ImageCropPreviewController(
                image: nil, // 可传当前背景图UIImage
                imageURL: URL(string: item.value), // 如有URL可传
                cropRatio: CGSize(width: 16, height: 9),
                confirmButtonTitle: "更换背景图",
                descriptionText: ""
            ) { [weak self] newImage, urlStr in
                guard let self = self else { return }
                // 更新保存请求中的背景图URL
                self.saveRequest.backgroundImage = urlStr

                // 立即更新UI显示
                if let section = self.findSectionIndex(for: "背景图"), let row = self.findRowIndex(in: section, for: "背景图") {
                    var updatedItem = self.userInfoSections[section].items[row]
                    updatedItem.value = urlStr
                    updatedItem.isSelected = !urlStr.isEmpty
                    self.userInfoSections[section].items[row] = updatedItem

                    // 更新对应的cell显示
                    if let cell = self.tableView.cellForRow(at: IndexPath(row: row, section: section)) as? UserInfoCell {
                        cell.configureWithBackgroundImage(isSelected: true, imageUrl: urlStr)
                    }
                }

                print("[DEBUG] 背景图裁剪完成，URL: \(urlStr)")
            }
            present(previewVC, animated: true)
        } else if item.title == "生日" {
            // 1. 从 saveRequest 获取完整生日信息
            let currentBirthday: Date? = {
                if let dateStr = saveRequest.birthdayInfo?.birthday, !dateStr.isEmpty {
                    return DateFormatter.birthdayFormatter.date(from: dateStr)
                }
                return nil
            }()
            let isShowAge  = saveRequest.birthdayInfo?.isShowAge ?? false
            let isShowConstellation = saveRequest.birthdayInfo?.isShowConstellation ?? false
            let isShowBirthdayTag = isShowAge || isShowConstellation
            let showType: BirthdayShowType = {
                if isShowAge { return .age }
                if isShowConstellation { return .constellation }
                return .none
            }()
            
            // 2. 跳转到编辑生日页面
            let birthdayEditVC = UserInfoEditBirthdayViewController()
            birthdayEditVC.birthday = currentBirthday
            birthdayEditVC.isShowBirthdayTag = isShowBirthdayTag
            birthdayEditVC.showType = showType
            birthdayEditVC.onConfirm = { [weak self] newBirthday, newShowTag, newShowType in
                guard let self = self else { return }
                if let section = self.findSectionIndex(for: "生日"), let row = self.findRowIndex(in: section, for: "生日") {
                    var updatedItem = self.userInfoSections[section].items[row]
                    if let date = newBirthday {
                        updatedItem.value = DateFormatter.birthdayFormatter.string(from: date)
                        updatedItem.isSelected = true
                        self.ensureBirthdayInfoNotNil()
                        self.saveRequest.birthdayInfo?.birthday = DateFormatter.birthdayFormatter.string(from: date)
                    } else {
                        updatedItem.value = "选择生日"
                        updatedItem.isSelected = false
                        self.ensureBirthdayInfoNotNil()
                        self.saveRequest.birthdayInfo?.birthday = ""
                    }
                    self.userInfoSections[section].items[row] = updatedItem
                    self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
                    self.ensureBirthdayInfoNotNil()
                    switch newShowType {
                    case .age:
                        self.saveRequest.birthdayInfo?.isShowAge = newShowTag
                        self.saveRequest.birthdayInfo?.isShowConstellation = false
                    case .constellation:
                        self.saveRequest.birthdayInfo?.isShowConstellation = newShowTag
                        self.saveRequest.birthdayInfo?.isShowAge = false
                    case .none:
                        self.saveRequest.birthdayInfo?.isShowAge = false
                        self.saveRequest.birthdayInfo?.isShowConstellation = false
                    }
                }
            }
            self.navigationController?.pushViewController(birthdayEditVC, animated: true)
        } else if item.title == "性别" {
            handleGenderEdit(item)
        } else if item.title == "职业" {
            handleOccupationEdit(item)
        } else if item.title == "学校" {
            handleSchoolEdit(item)
        } else if item.title == "地区" {
            handleAddressEdit(item)
        }
    }
    
    // 设置section之间的间距
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return section == 0 ? 0.01 : 12 // 第一个section不需要额外的header
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = UIView()
        headerView.backgroundColor = .clear
        return headerView
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01 // 最小值，实际上是不显示
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return nil
    }
    
    // MARK: - 名字编辑处理
    
    private func handleNameEdit(_ item: UserInfoItem) {
        // 检查是否允许修改名字
        if let isAllowModify = customerInfoData?.nickName?.isAllowModify, !isAllowModify {
            showToast("7天内仅可修改一次名字。")
            return
        }
        
        let nameEditVC = UserInfoEditNameViewController()
        nameEditVC.currentName = item.value
        nameEditVC.onNameUpdated = { [weak self] newName in
            guard let self = self else { return }
            
            // 更新名字
            if let section = self.findSectionIndex(for: "名字"), let row = self.findRowIndex(in: section, for: "名字") {
                var updatedItem = self.userInfoSections[section].items[row]
                updatedItem.value = newName
                updatedItem.isSelected = true
                self.userInfoSections[section].items[row] = updatedItem
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
                
                // 更新保存请求
                if self.saveRequest.nickName == "" {
                    self.saveRequest.nickName = NickName().name
                }
                self.saveRequest.nickName = newName
            }
        }
        
        self.navigationController?.pushViewController(nameEditVC, animated: true)
    }

    // MARK: - 地区编辑处理
    private func handleAddressEdit(_ item: UserInfoItem) {
        let addressEditVC = UserInfoEditAddressViewController()
        addressEditVC.currentAddress = item.value
        addressEditVC.onConfirm = { [weak self] regionId, newAddress, newIsShowPublicly in
            guard let self = self else { return }
            // 更新数据源
            if let section = self.findSectionIndex(for: "地区"), let row = self.findRowIndex(in: section, for: "地区") {
                var updatedItem = self.userInfoSections[section].items[row]
                updatedItem.value = newAddress
                updatedItem.isSelected = !newAddress.isEmpty
                self.userInfoSections[section].items[row] = updatedItem
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
            }
            // 保存到 saveRequest.region
            let names = newAddress.components(separatedBy: " ").filter { !$0.isEmpty }
            self.saveRequest.region = Region(isShow: newIsShowPublicly, regionIds: regionId.isEmpty ? nil : [regionId], regionNames: names)
        }
        self.navigationController?.pushViewController(addressEditVC, animated: true)
    }
    
    
    // MARK: - 树小柒号编辑处理
    
    private func handleNumEdit(_ item: UserInfoItem) {
        // 检查是否允许修改树小柒号
        if let isAllowModify = customerInfoData?.customerAccount?.isAllowModify, !isAllowModify {
            showToast("树小柒号是账号的唯一凭证，只能修改一次。")
            //这里可能存在bug,即回传本身系统分配的树小柒号,也算修改过.没时间验证.
            return
        }
        
        let numEditVC = UserInfoEditNumViewController()
        numEditVC.currentNum = item.value
        numEditVC.onNumUpdated = { [weak self] newNum in
            guard let self = self else { return }
            
            // 更新树小柒号
            if let section = self.findSectionIndex(for: "树小柒号"), let row = self.findRowIndex(in: section, for: "树小柒号") {
                var updatedItem = self.userInfoSections[section].items[row]
                updatedItem.value = newNum
                updatedItem.isSelected = true
                self.userInfoSections[section].items[row] = updatedItem
                self.tableView.reloadRows(at: [IndexPath(row: row, section: section)], with: .none)
                
                // 标记已经编辑过树小柒号
                self.hasEditedTreeNum = true
                
                // 保存到UserDefaults以便下次启动App时仍能记住状态
                UserDefaults.standard.set(true, forKey: "HasEditedTreeNum")
                UserDefaults.standard.synchronize()
                
                // 更新保存请求
                if self.saveRequest.customerAccount == "" {
                    self.saveRequest.customerAccount = CustomerAccount().account
                }
                self.saveRequest.customerAccount = newNum
            }
        }
        
        self.navigationController?.pushViewController(numEditVC, animated: true)
    }
    
    // MARK: - 辅助方法
    
    private func findSectionIndex(for title: String) -> Int? {
        for (sectionIndex, section) in userInfoSections.enumerated() {
            for item in section.items {
                if item.title == title {
                    return sectionIndex
                }
            }
        }
        return nil
    }
    
    private func findRowIndex(in section: Int, for title: String) -> Int? {
        for (rowIndex, item) in userInfoSections[section].items.enumerated() {
            if item.title == title {
                return rowIndex
            }
        }
        return nil
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        let cornerRadius: CGFloat = 5
        let totalRows = tableView.numberOfRows(inSection: indexPath.section)
        let maskPath: UIBezierPath
        
        if totalRows == 1 {
            // 只有一行，四个角都圆
            maskPath = UIBezierPath(roundedRect: cell.bounds, cornerRadius: cornerRadius)
        } else if indexPath.row == 0 {
            // 第一行，左上右上圆
            maskPath = UIBezierPath(roundedRect: cell.bounds,
                                    byRoundingCorners: [.topLeft, .topRight],
                                    cornerRadii: CGSize(width: cornerRadius, height: cornerRadius))
        } else if indexPath.row == totalRows - 1 {
            // 最后一行，左下右下圆
            maskPath = UIBezierPath(roundedRect: cell.bounds,
                                    byRoundingCorners: [.bottomLeft, .bottomRight],
                                    cornerRadii: CGSize(width: cornerRadius, height: cornerRadius))
        } else {
            // 中间行，无圆角
            maskPath = UIBezierPath(rect: cell.bounds)
        }
        
        let maskLayer = CAShapeLayer()
        maskLayer.path = maskPath.cgPath
        cell.contentView.layer.mask = maskLayer
        cell.contentView.layer.masksToBounds = true
        cell.backgroundColor = .clear
        cell.contentView.backgroundColor = .white
    }
}

// MARK: - 自定义Cell

class UserInfoCell: UITableViewCell {
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 5
        view.layer.masksToBounds = true
        return view
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#777777")
        return label
    }()
    
    private let valueLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#333333")
        label.textAlignment = .left // 左对齐
        label.numberOfLines = 0 // 支持多行
        return label
    }()
    
    private let backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .gray
        imageView.layer.cornerRadius = 5
        return imageView
    }()
    
    private let disclosureImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "user_info_edit_list_rigth_arrow")
        imageView.tintColor = .lightGray
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        contentView.addSubview(containerView)
        // 设置containerView的约束为contentView的inset（如8pt）
        containerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -4),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16)
        ])
        // 再把titleLabel等都加到containerView上
        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        containerView.addSubview(disclosureImageView)
        
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        valueLabel.translatesAutoresizingMaskIntoConstraints = false
        disclosureImageView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            titleLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            
            disclosureImageView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            disclosureImageView.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            disclosureImageView.widthAnchor.constraint(equalToConstant: 32),
            disclosureImageView.heightAnchor.constraint(equalToConstant: 32),
            
            // 值标签固定在距离左边110pt的位置
            valueLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 110),
            valueLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            valueLabel.trailingAnchor.constraint(lessThanOrEqualTo: disclosureImageView.leadingAnchor, constant: -10),
        ])
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configure(with title: String, value: String, hasDisclosure: Bool, isSelected: Bool, attributedValue: NSAttributedString? = nil) {
        titleLabel.text = title
        disclosureImageView.isHidden = !hasDisclosure
        if let attr = attributedValue {
            valueLabel.attributedText = attr
        } else {
            valueLabel.text = value
            valueLabel.textColor = isSelected ? UIColor(hex: "#333333") : UIColor(hex: "#AAAAAA")
        }
    }
    
    func configureWithBackgroundImage(isSelected: Bool = false, imageUrl: String? = nil) {
        // 只在第一次添加backgroundImageView时设置约束
        if backgroundImageView.superview == nil {
            containerView.addSubview(backgroundImageView)
            backgroundImageView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                backgroundImageView.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
                backgroundImageView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 110),
                backgroundImageView.widthAnchor.constraint(equalToConstant: 44),
                backgroundImageView.heightAnchor.constraint(equalToConstant: 26)
            ])
        }

        valueLabel.isHidden = true

        // 新逻辑：如果imageUrl为nil或空，显示浅灰色纯色背景，否则加载网络图片
        if let urlStr = imageUrl, !urlStr.isEmpty, let url = URL(string: urlStr) {
            backgroundImageView.kf.setImage(with: url, placeholder: nil)
            backgroundImageView.backgroundColor = .clear
        } else {
            backgroundImageView.image = nil
            backgroundImageView.backgroundColor = UIColor(hex: "#F5F5F5") // 浅灰色
        }
        backgroundImageView.alpha = isSelected ? 1.0 : 0.6
    }
}

// MARK: - 日期格式化工具

fileprivate extension DateFormatter {
    static let birthdayFormatter: DateFormatter = {
        let df = DateFormatter()
        df.dateFormat = "yyyy-MM-dd"
        return df
    }()
}

// MARK: - 自定义生日选择弹窗
class BirthdayPickerPopup: UIView {
    var onCancel: (() -> Void)?
    var onConfirm: ((Date) -> Void)?
    
    private let picker = UIDatePicker()
    private let container = UIView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = UIColor.black.withAlphaComponent(0.4)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(container)
        container.backgroundColor = .white
        container.layer.cornerRadius = 12
        container.clipsToBounds = true
        container.translatesAutoresizingMaskIntoConstraints = false
        
        let toolbar = UIView()
        container.addSubview(toolbar)
        toolbar.translatesAutoresizingMaskIntoConstraints = false
        
        let cancelBtn = UIButton(type: .system)
        cancelBtn.setTitle("取消", for: .normal)
        cancelBtn.setTitleColor(UIColor(hex: "#AAAAAA"), for: .normal)
        cancelBtn.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        cancelBtn.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        toolbar.addSubview(cancelBtn)
        cancelBtn.translatesAutoresizingMaskIntoConstraints = false
        
        let confirmBtn = UIButton(type: .system)
        confirmBtn.setTitle("保存", for: .normal)
        confirmBtn.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        confirmBtn.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        confirmBtn.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        toolbar.addSubview(confirmBtn)
        confirmBtn.translatesAutoresizingMaskIntoConstraints = false
        
        let titleLabel = UILabel()
        titleLabel.text = "选择你的生日"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textAlignment = .center
        titleLabel.textColor = UIColor(hex: "#333333")
        toolbar.addSubview(titleLabel)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        
        picker.datePickerMode = .date
        picker.maximumDate = Date()
        picker.locale = Locale(identifier: "zh_CN")
        picker.preferredDatePickerStyle = .wheels
        container.addSubview(picker)
        picker.translatesAutoresizingMaskIntoConstraints = false
        
        let containerHeight: CGFloat = 300
        NSLayoutConstraint.activate([
            container.leadingAnchor.constraint(equalTo: leadingAnchor),
            container.trailingAnchor.constraint(equalTo: trailingAnchor),
            container.bottomAnchor.constraint(equalTo: bottomAnchor),
            container.heightAnchor.constraint(equalToConstant: containerHeight),
            
            toolbar.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            toolbar.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            toolbar.topAnchor.constraint(equalTo: container.topAnchor),
            toolbar.heightAnchor.constraint(equalToConstant: 44),
            
            cancelBtn.leadingAnchor.constraint(equalTo: toolbar.leadingAnchor, constant: 16),
            cancelBtn.centerYAnchor.constraint(equalTo: toolbar.centerYAnchor),
            
            confirmBtn.trailingAnchor.constraint(equalTo: toolbar.trailingAnchor, constant: -16),
            confirmBtn.centerYAnchor.constraint(equalTo: toolbar.centerYAnchor),
            
            titleLabel.centerXAnchor.constraint(equalTo: toolbar.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: toolbar.centerYAnchor),
            
            picker.leadingAnchor.constraint(equalTo: container.leadingAnchor),
            picker.trailingAnchor.constraint(equalTo: container.trailingAnchor),
            picker.topAnchor.constraint(equalTo: toolbar.bottomAnchor),
            picker.bottomAnchor.constraint(equalTo: container.bottomAnchor)
        ])
        
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        self.addGestureRecognizer(tap)
    }
    
    func setDate(_ date: Date) {
        picker.date = date
    }
    
    @objc private func cancelTapped() {
        onCancel?()
        removeFromSuperview()
    }
    
    @objc private func confirmTapped() {
        onConfirm?(picker.date)
        removeFromSuperview()
    }
    
    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: self)
        if !container.frame.contains(location) {
            onCancel?()
            removeFromSuperview()
        }
    }
}

// MARK: - 保存请求参数快速获取工具
extension UserInformationEditingPage {
    /// 根据标题在 `userInfoSections` 中查找当前值，辅助调试或扩展
    fileprivate func valueFor(title: String) -> String? {
        for section in userInfoSections {
            if let item = section.items.first(where: { $0.title == title }) {
                return item.value
            }
        }
        return nil
    }
}

extension UserInformationEditingPage {
    /// 确保 saveRequest.birthdayInfo 已实例化
    fileprivate func ensureBirthdayInfoNotNil() {
        if saveRequest.birthdayInfo == nil {
            saveRequest.birthdayInfo = BirthdayInfo()
        }
    }
}

// MARK: - 简介高亮工具
extension UserInformationEditingPage {
    /// 将@用户id\\替换为@用户名并高亮，返回可直接赋值给UILabel的NSAttributedString
    /// - Parameters:
    ///   - snapshot: 原始文本（如"广州市公交总公司@40288ae791cb1f7c0191cb227d3d0003\\"）
    ///   - mentionedUser: [用户id: 用户名] 字典
    /// - Returns: 替换并高亮@用户名的NSAttributedString
    static func makeHighlightedSignature(from snapshot: String, mentionedUser: [String: String]) -> NSAttributedString {
        print("[Debug] 原始文本: \(snapshot)")
        print("[Debug] 提及用户: \(mentionedUser)")
        
        // 创建一个特殊标记字典，用于临时替换
        var markers: [String: (id: String, name: String)] = [:]
        var processedText = snapshot
        
        // 第一步：找出所有@id\\并替换为特殊标记
        let pattern = "@([a-zA-Z0-9]+)\\\\"
        guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
            return NSAttributedString(string: snapshot, attributes: [.font: UIFont.systemFont(ofSize: 14)])
        }
        
        let nsString = snapshot as NSString
        let matches = regex.matches(in: snapshot, options: [], range: NSRange(location: 0, length: nsString.length))
        
        print("[Debug] 找到 \(matches.count) 个匹配")
        
        // 从后向前替换，避免索引错位
        for (index, match) in matches.enumerated().reversed() {
            let idRange = match.range(at: 1)
            let fullRange = match.range(at: 0)
            let id = nsString.substring(with: idRange)
            
            if let userName = mentionedUser[id] {
                // 创建唯一标记
                let marker = "@@MARKER_\(index)@@"
                markers[marker] = (id: id, name: userName)
                
                // 替换为标记
                let currentNSString = processedText as NSString
                processedText = currentNSString.replacingCharacters(in: fullRange, with: marker)
                
                print("[Debug] 替换 @\(id)\\ 为标记 \(marker)")
            }
        }
        
        print("[Debug] 标记替换后: \(processedText)")
        
        // 第二步：将所有标记替换为@用户名+零宽空格
        var finalText = processedText
        var highlightRanges: [(range: NSRange, id: String)] = []
        
        // 确保按照标记的索引顺序处理，从0开始
        for i in 0..<markers.count {
            let marker = "@@MARKER_\(i)@@"
            guard let info = markers[marker] else { continue }
            
            // 显示名称加上零宽空格分隔符
            let displayName = "@\(info.name)\u{200B}"
            
            // 查找标记位置
            if let range = finalText.range(of: marker) {
                let nsRange = NSRange(range, in: finalText)
                
                // 记录高亮范围（不包括零宽空格）
                let highlightLength = displayName.count - 1 // 减去零宽空格的长度
                highlightRanges.append((range: NSRange(location: nsRange.location, length: highlightLength), id: info.id))
                
                // 替换为显示名称（包含零宽空格）
                finalText = finalText.replacingOccurrences(of: marker, with: displayName)
                
                print("[Debug] 将标记 \(marker) 替换为 \(displayName) 在位置 \(nsRange.location)")
            }
        }
        
        print("[Debug] 最终文本: \(finalText)")
        
        // 第三步：创建富文本并添加高亮
        let finalAttr = NSMutableAttributedString(string: finalText, attributes: [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor(hex: "#333333")
        ])
        
        // 应用高亮
        for highlight in highlightRanges {
            if highlight.range.location + highlight.range.length <= finalAttr.length {
                finalAttr.addAttribute(.foregroundColor, value: UIColor.systemBlue, range: highlight.range)
                finalAttr.addAttribute(NSAttributedString.Key("mentionId"), value: highlight.id, range: highlight.range)
                print("[Debug] 应用高亮 ID: \(highlight.id) 范围: \(highlight.range)")
            } else {
                print("[Error] 高亮范围超出文本长度: ID: \(highlight.id) 范围: \(highlight.range), 文本长度: \(finalAttr.length)")
            }
        }
        
        // 检查最终富文本的属性
        print("[Debug] 检查富文本属性:")
        finalAttr.enumerateAttribute(.foregroundColor, in: NSRange(location: 0, length: finalAttr.length), options: []) { value, range, _ in
            if let color = value as? UIColor, color == UIColor.systemBlue {
                print("[Debug] 发现蓝色文本范围: \(range)")
            }
        }
        finalAttr.enumerateAttribute(NSAttributedString.Key("mentionId"), in: NSRange(location: 0, length: finalAttr.length), options: []) { value, range, _ in
            if let id = value as? String {
                print("[Debug] 发现mentionId属性: \(id) 范围: \(range)")
            }
        }
        
        return finalAttr
    }
}

