//
//  UserInfoEditAddressViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/5/20.
//
//  地址编辑内页

import UIKit

class UserInfoEditAddressViewController: BaseViewController {

    // MARK: - 外部参数
    var currentAddress: String = ""
    var isShowAddressPublicly: Bool = false
    // 回调: (选中id, 名称, 是否公开)
    var onConfirm: ((String, String, Bool) -> Void)?

    // 记录初始值（如需变色交互可扩展）
    private var initialAddress: String = ""
    private var initialIsShowAddressPublicly: Bool = false
    
    // 存储服务器返回的原始地址名称数组，用于"已选地区"标记
    private var serverStoredRegionNames: [String] = []

    // 保存选中的id（完善为支持多级地址）
    private var selectedRegionId: String = ""

    // MARK: - UI
    private let addressCard = UIView()
    private let addressTitleLabel = UILabel()
    private let addressValueLabel = UILabel()
    private let addressButton = UIButton(type: .system)

    private let publicTitleLabel = UILabel()
    private let publicCard = UIView()
    private let publicSwitchLabel = UILabel()
    private let publicSwitch = UISwitch()

    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "编辑地区"
        rightNavTitle = "确认"
        rightNavButtonTintColor = UIColor(hex: "#FF6236", alpha: 0.6)
        rightNavAction = #selector(confirmTapped)
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")

        // 记录初始值
        initialAddress = currentAddress
        initialIsShowAddressPublicly = isShowAddressPublicly
        
        // 提取服务器返回的地址名称数组，用于标记"已选地区"
        serverStoredRegionNames = initialAddress.components(separatedBy: " ").filter { !$0.isEmpty }

        setupUI()
        updateUI()
        updateConfirmButtonState()
    }

    private func setupUI() {
        // 地区卡片
        addressCard.backgroundColor = .white
        addressCard.layer.cornerRadius = 8
        addressCard.layer.masksToBounds = true
        addressCard.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(addressCard)
        NSLayoutConstraint.activate([
            addressCard.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 24),
            addressCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            addressCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            addressCard.heightAnchor.constraint(equalToConstant: 56)
        ])

        addressTitleLabel.text = "地区"
        addressTitleLabel.font = UIFont.systemFont(ofSize: 16)
        addressTitleLabel.textColor = UIColor(hex: "#333333")
        addressTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        addressCard.addSubview(addressTitleLabel)
        NSLayoutConstraint.activate([
            addressTitleLabel.leadingAnchor.constraint(equalTo: addressCard.leadingAnchor, constant: 16),
            addressTitleLabel.centerYAnchor.constraint(equalTo: addressCard.centerYAnchor)
        ])

        addressValueLabel.font = UIFont.systemFont(ofSize: 16)
        addressValueLabel.textColor = UIColor(hex: "#AAAAAA")
        addressValueLabel.textAlignment = .right
        addressValueLabel.translatesAutoresizingMaskIntoConstraints = false
        addressCard.addSubview(addressValueLabel)
        NSLayoutConstraint.activate([
            addressValueLabel.centerYAnchor.constraint(equalTo: addressCard.centerYAnchor),
            addressValueLabel.trailingAnchor.constraint(equalTo: addressCard.trailingAnchor, constant: -40),
            addressValueLabel.leadingAnchor.constraint(greaterThanOrEqualTo: addressTitleLabel.trailingAnchor, constant: 8)
        ])

        let arrow = UIImageView(image: UIImage(named: "user_info_edit_list_rigth_arrow"))
        arrow.contentMode = .scaleAspectFit
        arrow.translatesAutoresizingMaskIntoConstraints = false
        addressCard.addSubview(arrow)
        NSLayoutConstraint.activate([
            arrow.centerYAnchor.constraint(equalTo: addressCard.centerYAnchor),
            arrow.trailingAnchor.constraint(equalTo: addressCard.trailingAnchor, constant: -16),
            arrow.widthAnchor.constraint(equalToConstant: 20),
            arrow.heightAnchor.constraint(equalToConstant: 20)
        ])

        // 整行可点
        addressButton.backgroundColor = .clear
        addressButton.translatesAutoresizingMaskIntoConstraints = false
        addressButton.addTarget(self, action: #selector(addressTapped), for: .touchUpInside)
        addressCard.addSubview(addressButton)
        NSLayoutConstraint.activate([
            addressButton.topAnchor.constraint(equalTo: addressCard.topAnchor),
            addressButton.leadingAnchor.constraint(equalTo: addressCard.leadingAnchor),
            addressButton.trailingAnchor.constraint(equalTo: addressCard.trailingAnchor),
            addressButton.bottomAnchor.constraint(equalTo: addressCard.bottomAnchor)
        ])

        // 是否公开展示
        publicTitleLabel.text = "是否公开展示"
        publicTitleLabel.font = UIFont.systemFont(ofSize: 12)
        publicTitleLabel.textColor = UIColor(hex: "#999999")
        publicTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(publicTitleLabel)
        NSLayoutConstraint.activate([
            publicTitleLabel.topAnchor.constraint(equalTo: addressCard.bottomAnchor, constant: 24),
            publicTitleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 31)
        ])

        publicCard.backgroundColor = .white
        publicCard.layer.cornerRadius = 8
        publicCard.layer.masksToBounds = true
        publicCard.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(publicCard)
        NSLayoutConstraint.activate([
            publicCard.topAnchor.constraint(equalTo: publicTitleLabel.bottomAnchor, constant: 8),
            publicCard.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            publicCard.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            publicCard.heightAnchor.constraint(equalToConstant: 50)
        ])

        publicSwitchLabel.text = "展示地区标签"
        publicSwitchLabel.font = UIFont.systemFont(ofSize: 16)
        publicSwitchLabel.textColor = UIColor(hex: "#222222")
        publicSwitchLabel.translatesAutoresizingMaskIntoConstraints = false
        publicCard.addSubview(publicSwitchLabel)
        NSLayoutConstraint.activate([
            publicSwitchLabel.leadingAnchor.constraint(equalTo: publicCard.leadingAnchor, constant: 16),
            publicSwitchLabel.centerYAnchor.constraint(equalTo: publicCard.centerYAnchor)
        ])

        publicSwitch.isOn = isShowAddressPublicly
        publicSwitch.onTintColor = UIColor(hex: "#FF6236")
        publicSwitch.translatesAutoresizingMaskIntoConstraints = false
        publicSwitch.addTarget(self, action: #selector(publicSwitchChanged), for: .valueChanged)
        publicCard.addSubview(publicSwitch)
        NSLayoutConstraint.activate([
            publicSwitch.trailingAnchor.constraint(equalTo: publicCard.trailingAnchor, constant: -16),
            publicSwitch.centerYAnchor.constraint(equalTo: publicCard.centerYAnchor)
        ])
    }

    private func updateUI() {
        addressValueLabel.text = currentAddress.isEmpty ? "请选择" : currentAddress
        addressValueLabel.textColor = currentAddress.isEmpty ? UIColor(hex: "#AAAAAA") : UIColor(hex: "#333333")
        publicSwitch.isOn = isShowAddressPublicly
        updateConfirmButtonState()
    }

    @objc private func addressTapped() {
        // 预留：弹出地区选择器
        print("点击了地区选择")
        //跳转到地址选择器中
        let addressListVC = UserInfoEditAddressList()
        // 传递服务器存储的地址名称数组，用于"已选地区"标记
        addressListVC.selectedCountryNames = serverStoredRegionNames
        addressListVC.onAddressSelected = { [weak self] id, name in
            self?.selectedRegionId = id
            self?.currentAddress = name
            DispatchQueue.main.async { self?.updateUI() }
            self?.updateConfirmButtonState()
        }
        self.navigationController?.pushViewController(addressListVC, animated: true)
    }

    @objc private func publicSwitchChanged() {
        isShowAddressPublicly = publicSwitch.isOn
        updateConfirmButtonState()
    }

    @objc private func confirmTapped() {
        // 确保只有在有地址更改时才能确认
        if currentAddress.isEmpty || selectedRegionId.isEmpty {
            // 可以添加提示
            let alert = UIAlertController(
                title: "无法保存", 
                message: "请先选择一个有效的地区", 
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }
        
        onConfirm?(selectedRegionId, currentAddress, isShowAddressPublicly)
        navigationController?.popViewController(animated: true)
    }

    // MARK: - Helpers
    private func updateConfirmButtonState() {
        let hasChanged = (currentAddress != initialAddress) || (isShowAddressPublicly != initialIsShowAddressPublicly)
        rightNavButtonTintColor = hasChanged ? UIColor.red : UIColor(hex: "#FF6236", alpha: 0.6)
        rightNavButton?.isEnabled = hasChanged && !currentAddress.isEmpty
        rightNavButton?.setTitleColor(rightNavButtonTintColor, for: .normal)
    }
}
